import styles from './AboutUs.module.scss';
import DecryptedText from '../DecryptedText';

/**
 * AboutUs Component
 * 
 * A responsive About Us section component that displays information about the company
 * using SVG directly from Figma for perfect design fidelity.
 * 
 * @returns {JSX.Element} The AboutUs component
 */
const AboutUs = () => {
  return (
    <div className={styles.aboutUsSection}>
      <img
        src="/about_us.svg"
        alt="About Us section with company information and expertise details"
        className={styles.aboutUsSvg}
      />
      <div className={styles.textBox}>
        <div className={styles.textContent}>
          <DecryptedText
            text={`THROUGH YEARS OF TECHNICAL CULTIVATION, WE'VE
FORGED DEEP EXPERTISE ACROSS DOMAINS.
PARTNERING WITH CLIENTS THROUGH EVERY
DEVELOPMENT PHASE WITH DEDICATED
CRAFTSMANSHIP, OUR COMMITMENT HAS EARNED
ACCLAIM FROM DIVERSE INDUSTRIES. GROUNDED IN
TODAY'S TECH LANDSCAPE, WE PIONEER OPEN
INNOVATION TO EMPOWER GLOBAL BUSINESSES IN
NAVIGATING DIGITAL TRANSFORMATION,
COLLECTIVELY BUILDING A MORE EFFICIENT AND
CONNECTED FUTURE.`}
            animateOn="view"
            revealDirection="center"
            sequential={true}
            speed={15}
            maxIterations={20}
            className={styles.revealedText}
            encryptedClassName={styles.encryptedText}
          />
        </div>
      </div>
    </div>
  );
};

export default AboutUs;
