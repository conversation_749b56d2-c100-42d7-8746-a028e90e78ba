import styles from './AboutUs.module.scss';
import DecryptedText from '../DecryptedText';

/**
 * AboutUs Component
 * 
 * A responsive About Us section component that displays information about the company
 * using SVG directly from Figma for perfect design fidelity.
 * 
 * @returns {JSX.Element} The AboutUs component
 */
const AboutUs = () => {
  // 解密动画参数配置 - 统一调整闪烁频率
  const decryptSpeed = 100;        // 闪烁间隔(ms) - 数值越大闪烁越慢
  const decryptIterations = 4;    // 变化次数 - 保持 speed × iterations ≈ 400

  return (
    <div className={styles.aboutUsSection}>
      <img
        src="/about_us.svg"
        alt="About Us section with company information and expertise details"
        className={styles.aboutUsSvg}
      />
      <div className={styles.textBox}>
        <div className={styles.textContent}>
          <div className={styles.textLine}>
            <DecryptedText
              text="THROUGH YEARS OF TECHNICAL CULTIVATION, WE'VE"
              animateOn="view"
              revealDirection="start"
              sequential={true}
              speed={decryptSpeed}
              maxIterations={decryptIterations}
              className={styles.revealedText}
              encryptedClassName={styles.encryptedText}
            />
          </div>
          <div className={styles.textLine}>
            <DecryptedText
              text="FORGED DEEP EXPERTISE ACROSS DOMAINS."
              animateOn="view"
              revealDirection="start"
              sequential={true}
              speed={decryptSpeed}
              maxIterations={decryptIterations}
              className={styles.revealedText}
              encryptedClassName={styles.encryptedText}
            />
          </div>
          <div className={styles.textLine}>
            <DecryptedText
              text="PARTNERING WITH CLIENTS THROUGH EVERY"
              animateOn="view"
              revealDirection="start"
              sequential={true}
              speed={decryptSpeed}
              maxIterations={decryptIterations}
              className={styles.revealedText}
              encryptedClassName={styles.encryptedText}
            />
          </div>
          <div className={styles.textLine}>
            <DecryptedText
              text="DEVELOPMENT PHASE WITH DEDICATED"
              animateOn="view"
              revealDirection="start"
              sequential={true}
              speed={decryptSpeed}
              maxIterations={decryptIterations}
              className={styles.revealedText}
              encryptedClassName={styles.encryptedText}
            />
          </div>
          <div className={styles.textLine}>
            <DecryptedText
              text="CRAFTSMANSHIP, OUR COMMITMENT HAS EARNED"
              animateOn="view"
              revealDirection="start"
              sequential={true}
              speed={decryptSpeed}
              maxIterations={decryptIterations}
              className={styles.revealedText}
              encryptedClassName={styles.encryptedText}
            />
          </div>
          <div className={styles.textLine}>
            <DecryptedText
              text="ACCLAIM FROM DIVERSE INDUSTRIES. GROUNDED IN"
              animateOn="view"
              revealDirection="start"
              sequential={true}
              speed={decryptSpeed}
              maxIterations={decryptIterations}
              className={styles.revealedText}
              encryptedClassName={styles.encryptedText}
            />
          </div>
          <div className={styles.textLine}>
            <DecryptedText
              text="TODAY'S TECH LANDSCAPE, WE PIONEER OPEN"
              animateOn="view"
              revealDirection="start"
              sequential={true}
              speed={decryptSpeed}
              maxIterations={decryptIterations}
              className={styles.revealedText}
              encryptedClassName={styles.encryptedText}
            />
          </div>
          <div className={styles.textLine}>
            <DecryptedText
              text="INNOVATION TO EMPOWER GLOBAL BUSINESSES IN"
              animateOn="view"
              revealDirection="start"
              sequential={true}
              speed={decryptSpeed}
              maxIterations={decryptIterations}
              className={styles.revealedText}
              encryptedClassName={styles.encryptedText}
            />
          </div>
          <div className={styles.textLine}>
            <DecryptedText
              text="NAVIGATING DIGITAL TRANSFORMATION,"
              animateOn="view"
              revealDirection="start"
              sequential={true}
              speed={decryptSpeed}
              maxIterations={decryptIterations}
              className={styles.revealedText}
              encryptedClassName={styles.encryptedText}
            />
          </div>
          <div className={styles.textLine}>
            <DecryptedText
              text="COLLECTIVELY BUILDING A MORE EFFICIENT AND"
              animateOn="view"
              revealDirection="start"
              sequential={true}
              speed={decryptSpeed}
              maxIterations={decryptIterations}
              className={styles.revealedText}
              encryptedClassName={styles.encryptedText}
            />
          </div>
          <div className={styles.textLine}>
            <DecryptedText
              text="CONNECTED FUTURE."
              animateOn="view"
              revealDirection="start"
              sequential={true}
              speed={decryptSpeed}
              maxIterations={decryptIterations}
              className={styles.revealedText}
              encryptedClassName={styles.encryptedText}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AboutUs;
