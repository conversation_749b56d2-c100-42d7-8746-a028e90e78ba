.aboutUsSection {
  width: 100%;
  height: auto;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin: 2rem 0;
  margin-left: 3.625rem;
  position: relative;

  @media (max-width: 768px) {
    margin: 1rem 0;
    margin-left: 1rem;
  }
}

.aboutUsSvg {
  width: 112.8125rem;
  height: auto;
  display: block;

  @media (max-width: 768px) {
    width: 100%;
    height: auto;
  }
}

// Text box positioning based on aboutUsSvg, converted to rem
.textBox {
  position: absolute;
  // 距离aboutUsSvg顶部249px，左部464px
  --top-px: 232;
  --left-px: 444;
  top: calc(var(--top-px) / 16 * 1rem); // 249px / 16 = 15.5625rem
  left: calc(var(--left-px) / 16 * 1rem); // 464px / 16 = 29rem

  // 文本框尺寸：1077*528px
  --width-px: 1077;
  --height-px: 528;
  width: calc(var(--width-px) / 16 * 1rem); // 1077px / 16 = 67.3125rem
  height: calc(var(--height-px) / 16 * 1rem); // 528px / 16 = 33rem

  // 设置边框为透明（不可见），保持布局结构
  border: 2px solid transparent;
  background-color: transparent;
  padding: 1rem;
  border-radius: 0.5rem;

  // 基本样式
  z-index: 10; // 确保在SVG之上

  .textContent {
    // 文本属性配置
    color: #262626;
    font-family: Inter, sans-serif;
    font-weight: 400;
    text-align: center;

    // 文本大小：40px
    --font-size-px: 40;
    font-size: calc(var(--font-size-px) / 16 * 1rem); // 40px / 16 = 2.5rem

    // Figma属性配置
    line-height: 1.2; // Line Height: 100%
    letter-spacing: 0; // Letter Spacing: 0%

    // 确保文本在容器中居中
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .textLine {
    // 每行文本的容器 - 强制布局控制
    width: 100%;
    display: block;
    text-align: center;
    margin: 0;
    padding: 0;

    // 强制限制文本不换行和溢出处理
    white-space: nowrap;
    overflow: hidden;

    // 确保每行高度固定
    height: 2.5rem; // 与字体大小一致，避免高度变化
    line-height: 2.5rem;

    // 为DecryptedText组件设置固定宽度
    > span {
      display: inline-block;
      max-width: 100%;
      overflow: hidden;
      white-space: nowrap;
    }
  }

  // DecryptedText动画样式
  .revealedText {
    // 已解密（显示）的文本样式
    opacity: 1;
    transition: opacity 0.1s ease;
  }

  .encryptedText {
    // 加密（随机字符）的文本样式
    opacity: 0.7;
    color: #888;
    transition: opacity 0.1s ease;
  }

  @media (max-width: 768px) {
    // 移动端响应式调整
    --top-px: 200;
    --left-px: 20;
    --width-px: 300;
    --height-px: 200;
    font-size: 0.9rem;
  }
}
