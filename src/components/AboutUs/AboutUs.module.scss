.aboutUsSection {
  width: 100%;
  height: auto;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin: 2rem 0;
  margin-left: 3.625rem;
  position: relative;

  @media (max-width: 768px) {
    margin: 1rem 0;
    margin-left: 1rem;
  }
}

.aboutUsSvg {
  width: 112.8125rem;
  height: auto;
  display: block;

  @media (max-width: 768px) {
    width: 100%;
    height: auto;
  }
}

// Text box positioning based on aboutUsSvg, converted to rem
.textBox {
  position: absolute;
  // 距离aboutUsSvg顶部249px，左部464px
  --top-px: 249;
  --left-px: 464;
  top: calc(var(--top-px) / 16 * 1rem); // 249px / 16 = 15.5625rem
  left: calc(var(--left-px) / 16 * 1rem); // 464px / 16 = 29rem

  // 文本框尺寸：1077*528px
  --width-px: 1077;
  --height-px: 528;
  width: calc(var(--width-px) / 16 * 1rem); // 1077px / 16 = 67.3125rem
  height: calc(var(--height-px) / 16 * 1rem); // 528px / 16 = 33rem

  // 暂时设置可见边框以便调试定位，背景透明
  border: 2px solid #ff0000;
  background-color: transparent;
  padding: 1rem;
  border-radius: 0.5rem;

  // 基本文字样式
  font-size: 1rem;
  color: #333;
  z-index: 10; // 确保在SVG之上

  @media (max-width: 768px) {
    // 移动端响应式调整
    --top-px: 200;
    --left-px: 20;
    --width-px: 300;
    --height-px: 200;
    font-size: 0.9rem;
  }
}
